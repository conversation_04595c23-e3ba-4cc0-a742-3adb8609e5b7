// ----------------------------------------------------------------------

const ROOTS = {
  AUTH: '/auth',
  DASHBOARD: '/dashboard',
};

// ----------------------------------------------------------------------

export const paths = {
  faqs: '/faqs',
  minimalStore: 'https://mui.com/store/items/minimal-dashboard/',
  // AUTH
  auth: {
    amplify: {
      signIn: `${ROOTS.AUTH}/amplify/sign-in`,
      verify: `${ROOTS.AUTH}/amplify/verify`,
      signUp: `${ROOTS.AUTH}/amplify/sign-up`,
      updatePassword: `${ROOTS.AUTH}/amplify/update-password`,
      resetPassword: `${ROOTS.AUTH}/amplify/reset-password`,
    },
    jwt: {
      signIn: `${ROOTS.AUTH}/jwt/sign-in`,
      signUp: `${ROOTS.AUTH}/jwt/sign-up`,
      forgotPassword: `${ROOTS.AUTH}/jwt/forgot-password`,
      newPassword: `${ROOTS.AUTH}/jwt/new-password`,
    },
    firebase: {
      signIn: `${ROOTS.AUTH}/firebase/sign-in`,
      verify: `${ROOTS.AUTH}/firebase/verify`,
      signUp: `${ROOTS.AUTH}/firebase/sign-up`,
      resetPassword: `${ROOTS.AUTH}/firebase/reset-password`,
    },
    auth0: {
      signIn: `${ROOTS.AUTH}/auth0/sign-in`,
    },
    supabase: {
      signIn: `${ROOTS.AUTH}/supabase/sign-in`,
      verify: `${ROOTS.AUTH}/supabase/verify`,
      signUp: `${ROOTS.AUTH}/supabase/sign-up`,
      updatePassword: `${ROOTS.AUTH}/supabase/update-password`,
      resetPassword: `${ROOTS.AUTH}/supabase/reset-password`,
    },
  },
  // DASHBOARD
  dashboard: {
    root: ROOTS.DASHBOARD,
    categories: {
      root: `${ROOTS.DASHBOARD}/categories`,
    },
    agents: {
      root: `${ROOTS.DASHBOARD}/agents`,
      clone: (id: number | string) => `${ROOTS.DASHBOARD}/clone-agent/${id}`,
      configureTool: `${ROOTS.DASHBOARD}/clone-agent/tools/callback`,
      chat: (id: number | string, agentId: string | number, cid: number | string) =>
        `${ROOTS.DASHBOARD}/clone-agent/${id}/agents/${agentId}/chat/${cid}`,
      templates: `${ROOTS.DASHBOARD}/agents/templates`,
      agents: `${ROOTS.DASHBOARD}/agents/my-agents`,
      // chat_messages_events: (id: number | string, agentId: string | number, cid: number | string) =>
      //   `${ROOTS.DASHBOARD}/clone-agent/${id}/agents/${agentId}/chat/${cid}/events`,
    },
    teams: {
      root: `${ROOTS.DASHBOARD}/teams`,
      clone: (id: number | string) => `${ROOTS.DASHBOARD}/clone-templates-team/${id}`,
      configureTool: `${ROOTS.DASHBOARD}/clone-templates-team/tools/callback`,
      chat: (id: number | string, agentId: string | number, cid: number | string) =>
        `${ROOTS.DASHBOARD}/clone-templates-team/${id}/tempaltes-team/${agentId}/chat/${cid}`,
      templates: `${ROOTS.DASHBOARD}/teams/templates`,
      my_team_templates: `${ROOTS.DASHBOARD}/teams/my-agents`,
    },
    knowledgeBase: `${ROOTS.DASHBOARD}/knowledge-base`,
    settings: `${ROOTS.DASHBOARD}/settings`,

    profile: {
      root: `${ROOTS.DASHBOARD}/profile`,
      knowledgeBase: `${ROOTS.DASHBOARD}/profile/knowledge-base`,
      preferences: `${ROOTS.DASHBOARD}/profile/preferences`,
      settings: `${ROOTS.DASHBOARD}/profile/settings`,
    },

    scheduled: {
      root: `${ROOTS.DASHBOARD}/scheduled-tasks`,
    },
  },
};
