import axiosInstance from 'src/utils/axios';
import { useApiServices } from 'src/services/hooks/use-api-services';

// Define the API endpoints for Agentss
export const taskEndpoints = {
  list: (cid: string | number) => `/chats/${cid}/tasks`,
  details: '/categories',
  enhance: (cid: number | string, aid: number | string) =>
    `/chats/${cid}/tasks/${aid}/enhance-task`,
  scheduled: (cid: number | string) => `/chats/${cid}/tasks/scheduled`,
};
// Define the Category interface
export type Tasks = {
  id: number;
  chatId: number;
  content: 'Please research the latest market trends';
  senderId: number;
  status: string;
  createdAt: string;
  updatedAt: string;
  metadata: {
    error: string;
    errorTime: string;
    eventType: string;
    taskResult: {
      data: string;
      status: string;
      timestamp: string;
    };
  };
  agentId: number;
};
export interface TasksResponse {
  tasks: Tasks[];
  total: number;
}
export interface TasksBody {
  task: string;
}

export type EnhanceTaskType = {
  data: {
    enhancedTask: string;
    userTask: string;
  };
};
export type ScheduledTask = {
  task: string;
  repeatType: string;
  startDate: string;
  endDate: string;
  interval: number;
};

// Define the API response structure

// Create a hook to use the Agentss API
export const useTasksApi = () => {
  const apiServices = useApiServices({ axiosInstance });

  // Get all Agentss
  const useGetTasks = (cid: string | number) => {
    return apiServices.useGetListService<TasksResponse>({
      url: taskEndpoints.list(cid),
      queryOptions: {
        enabled: +cid > 0 ? true : false,
      },
    });
  };

  // Get a single Agents by ID

  // Create a new Agents
  const useCreateTask = (cid: number | string, onSuccess?: (data: any) => void) => {
    return apiServices.usePostService<TasksBody, any>({
      url: taskEndpoints.list(cid),
      onSuccess,
    });
  };

  const useEnhanceTask = (
    cid: number | string,
    aid: string | number,
    onSuccess?: (data: any) => void
  ) => {
    return apiServices.usePostService<TasksBody, EnhanceTaskType>({
      url: taskEndpoints.enhance(cid, aid),
      onSuccess,
    });
  };

  return {
    useGetTasks,
    useCreateTask,
    useEnhanceTask,
  };
};
