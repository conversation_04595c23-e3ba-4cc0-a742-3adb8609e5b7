import axiosInstance from 'src/utils/axios';
import { useApiServices } from 'src/services/hooks/use-api-services';

// Define the API endpoints for Agentss
export const taskEndpoints = {
  list: `/tasks/schedule`,
  details: `/tasks/schedule`,
  toggle: (sid: number | string) => `/tasks/schedule/${sid}/toggle`,
};

export type ScheduledTaskItem = {
  id: number;
  taskId: number;
  repeatType: 'MONTHLY' | 'WEEKLY' | 'DAILY'; // enum from your schema
  startDate: string; // ISO date string
  endDate: string; // ISO date string
  lastRun: string | null;
  nextRun: string | null;
  interval: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  task: {
    id: number;
    chatId: number;
    content: string;
    senderId: number;
    status: 'SCHEDULED' | 'COMPLETED' | 'FAILED'; // guessing possible values
    agentId: number;
    userTeamId: number | null;
    createdAt: string;
    updatedAt: string;
    metadata: Record<string, unknown> | null;
  };
};
// Define the Category interface
export type ScheduledTaskResponse = {
  tasksScheduled: ScheduledTaskItem[];
  total: number;
};

export type ScheduledTask = {
  task: string;
  repeatType: string;
  startDate: string;
  endDate: string;
  interval: number;
};

// Define the API response structure

// Create a hook to use the Agentss API
export const useTasksSchedularApi = () => {
  const apiServices = useApiServices({ axiosInstance });

  // Get all Agentss
  const useGetTasks = (cid: string | number) => {
    return apiServices.useGetListService<ScheduledTaskResponse>({
      url: taskEndpoints.list,
      queryOptions: {
        enabled: +cid > 0 ? true : false,
      },
    });
  };

  const useShceduledTask = (onSuccess?: (data: any) => void) => {
    return apiServices.usePostService<ScheduledTask, any>({
      url: taskEndpoints.list,
      onSuccess,
    });
  };

  // Update a MyTeamTemplatess
  const useToogleScheduledTask = (sid: string, onSuccess?: () => void) => {
    return apiServices.usePatchService<any>({
      url: taskEndpoints.toggle(sid),
      onSuccess,
    });
  };

  // Delete a ShceduledTask
  const useDeleteShceduledTask = (onSuccess?: () => void) => {
    return apiServices.useDeleteService<any>({
      url: taskEndpoints.list,
      onSuccess,
    });
  };
  return {
    useGetTasks,
    useShceduledTask,
    useDeleteShceduledTask,
    useToogleScheduledTask,
  };
};
